import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter, innovatioIdeaStatus } from '../../utils/util';
import { logger } from '../../utils/logger';
import { simpleTriggers } from '../reward/simpleTriggers';



export const SuggestionBoxService = {

  getAllSuggestions: async (staffId: number, query: any) => {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        search,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);
      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        ...(status ? { status: status.toUpperCase() } : {}),
        ...(search
          ? {
              OR: [
                { content: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {}),
        ...dateFilter,
        ...(isMyIdeas ? { authorId: staffId } : {}),
      };

      const [suggestions, total] = await Promise.all([
        db.suggestionBox.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            staff: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
          },
        }),
        db.suggestionBox.count({ where: whereClause }),
      ]);

      return {
        suggestions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };
    } catch (error) {
      logger.error('Error getting suggestions:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch suggestions', 400);
    }
  },

 
};
