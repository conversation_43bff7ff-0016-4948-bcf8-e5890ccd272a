import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { createDateFilter, innovatioIdeaStatus } from '../../utils/util';
import { logger } from '../../utils/logger';
import { simpleTriggers } from '../reward/simpleTriggers';



export const SuggestionBoxService = {

  getAllSuggestions: async (staffId: number, query: any) => {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        isRead,
        anonymous,
        myIdeas = false,
      } = query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);
      const dateFilter = createDateFilter(startDate, endDate);

      const whereClause: any = {
        ...(search
          ? {
              OR: [
                { content: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {}),
        ...dateFilter,
        ...(myIdeas === 'true' || myIdeas === true ? { staffId: staffId } : {}),
        ...(isRead !== undefined ? { isRead: isRead === 'true' || isRead === true } : {}),
        ...(anonymous !== undefined ? { anonymous: anonymous === 'true' || anonymous === true } : {}),
      };

      const [suggestions, total] = await Promise.all([
        db.suggestionBox.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: {
            [sortBy]: sortOrder,
          },
          include: {
            staff: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
          },
        }),
        db.suggestionBox.count({ where: whereClause }),
      ]);

      return {
        suggestions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / take),
          totalItems: total,
          itemsPerPage: take,
        },
      };
    } catch (error) {
      logger.error('Error getting suggestions:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch suggestions', 400);
    }
  },

  createSuggestion: async (staffId: number, data: any) => {
    try {
      const { content, anonymous = false } = data;

      if (!content || content.trim().length === 0) {
        throw new HttpError('Suggestion content is required', 400);
      }

      const suggestion = await db.suggestionBox.create({
        data: {
          staffId,
          content: content.trim(),
          anonymous,
          isRead: false,
        },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      // Trigger reward for suggestion submission using custom event
      try {
        await simpleTriggers.onCustomEvent(staffId, 'suggestion_box', suggestion.id);
      } catch (rewardError) {
        logger.warn('Failed to trigger suggestion reward:', rewardError);
        // Don't fail the suggestion creation if reward fails
      }

      return suggestion;
    } catch (error) {
      logger.error('Error creating suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create suggestion', 400);
    }
  },

  updateSuggestion: async (_staffId: number, data: any, suggestionId: number) => {
    try {
      const { isRead, comment } = data;

      // Check if suggestion exists
      const existingSuggestion = await db.suggestionBox.findUnique({
        where: { id: suggestionId },
      });

      if (!existingSuggestion) {
        throw new HttpError('Suggestion not found', 404);
      }

      // Prepare update data
      const updateData: any = {};

      if (isRead !== undefined) {
        updateData.isRead = isRead;
      }

      if (comment !== undefined) {
        updateData.comment = comment;
      }

      if (Object.keys(updateData).length === 0) {
        throw new HttpError('No valid fields to update', 400);
      }

      const updatedSuggestion = await db.suggestionBox.update({
        where: { id: suggestionId },
        data: updateData,
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      return updatedSuggestion;
    } catch (error) {
      logger.error('Error updating suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update suggestion', 400);
    }
  },

  getSuggestionById: async (_staffId: number, suggestionId: number) => {
    try {
      const suggestion = await db.suggestionBox.findUnique({
        where: { id: suggestionId },
        include: {
          staff: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      if (!suggestion) {
        throw new HttpError('Suggestion not found', 404);
      }

      return suggestion;
    } catch (error) {
      logger.error('Error getting suggestion by ID:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch suggestion', 400);
    }
  },

  deleteSuggestion: async (staffId: number, suggestionId: number) => {
    try {
      // Check if suggestion exists
      const existingSuggestion = await db.suggestionBox.findUnique({
        where: { id: suggestionId },
      });

      if (!existingSuggestion) {
        throw new HttpError('Suggestion not found', 404);
      }

      // Check if the staff member owns the suggestion or has admin permissions
      let hasPermission = false;
      try {
        hasPermission = await staffHasPermission(staffId, PERMISSIONS.FEEDBACK_VIEW); // Using feedback permission as proxy for admin
      } catch (error) {
        // If permission check fails, user doesn't have admin permissions
        hasPermission = false;
      }

      if (existingSuggestion.staffId !== staffId && !hasPermission) {
        throw new HttpError('You do not have permission to delete this suggestion', 403);
      }

      await db.suggestionBox.delete({
        where: { id: suggestionId },
      });

      return { message: 'Suggestion deleted successfully' };
    } catch (error) {
      logger.error('Error deleting suggestion:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete suggestion', 400);
    }
  },


};
