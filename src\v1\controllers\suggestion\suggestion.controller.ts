import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { SuggestionBoxService } from '../../services/suggestion';

/**
 * <PERSON>le creating a new suggestion
 */
const CreateSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    SuggestionBoxService.createSuggestion,
    req.body,
    res,
    staffId
  );
};

/**
 * <PERSON>le listing all suggestions with pagination and filtering
 */
const ListSuggestionsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    SuggestionBoxService.getAllSuggestions,
    req.query,
    res,
    staffId
  );
};

/**
 * <PERSON>le getting a single suggestion by ID
 */
const GetSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { suggestionId } = req.params;
  controllerOperations(
    SuggestionBoxService.getSuggestionById,
    parseInt(suggestionId),
    res,
    staffId
  );
};

/**
 * Handle updating a suggestion (isRead, comment)
 */
const UpdateSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { suggestionId, ...updateData } = req.body;
  controllerOperations(
    SuggestionBoxService.updateSuggestion,
    updateData,
    res,
    staffId,
    suggestionId
  );
};

/**
 * Handle deleting a suggestion
 */
const DeleteSuggestionHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { suggestionId } = req.params;
  controllerOperations(
    SuggestionBoxService.deleteSuggestion,
    parseInt(suggestionId),
    res,
    staffId
  );
};

export const suggestionControllers = {
  CreateSuggestionHandler,
  ListSuggestionsHandler,
  GetSuggestionHandler,
  UpdateSuggestionHandler,
  DeleteSuggestionHandler,
};
