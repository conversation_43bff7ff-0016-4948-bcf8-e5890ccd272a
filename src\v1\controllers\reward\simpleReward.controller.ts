import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { simpleRewardService } from '../../services/reward/simpleReward';
import { simpleTriggers } from '../../services/reward/simpleTriggers';

/**
 * Simple Reward Controllers
 * Clean, straightforward API endpoints that match the simplified reward system
 */

// Core Reward Management
const ListRewardsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    simpleRewardService.listRewards,
    req.query,
    res,
    staffId
  );
};

const CreateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    simpleRewardService.createReward,
    req.body,
    res,
    staffId
  );
};

const UpdateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { rewardId, ...updateData } = req.body;
  controllerOperations(
    simpleRewardService.updateReward,
    updateData,
    res,
    staffId,
    rewardId
  );
};

const DeleteRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { rewardId } = req.params;
  controllerOperations(
    simpleRewardService.deleteReward,
    undefined,
    res,
    staffId,
    parseInt(rewardId)
  );
};

// Reward History and Analytics
const GetRewardHistoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    simpleRewardService.getRewardHistory,
    req.query,
    res,
    staffId
  );
};

const GetRewardStatisticsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    simpleRewardService.getRewardStatistics,
    req.query,
    res,
    staffId
  );
};

// Manual Reward Triggering (for testing/admin purposes)
const TriggerInnovationIdeaRewardHandler = (req: Request, res: Response) => {
  const { staffId, ideaId } = req.body;
  controllerOperations(
    simpleTriggers.onInnovationIdeaPosted,
    undefined,
    res,
    staffId,
    ideaId
  );
};

const TriggerInnovationCommentRewardHandler = (req: Request, res: Response) => {
  const { staffId, commentId, ideaId } = req.body;
  controllerOperations(
    simpleTriggers.onInnovationComment,
    undefined,
    res,
    staffId,
    commentId,
    ideaId
  );
};

const TriggerPackageReferralRewardHandler = (req: Request, res: Response) => {
  const { referrerStaffId, bookingId, referralCodeId } = req.body;
  controllerOperations(
    simpleTriggers.onPackageReferral,
    undefined,
    res,
    referrerStaffId,
    bookingId,
    referralCodeId
  );
};

// Referral Code Validation (keeping this from the old system)
const VerifyReferralCodeHandler = (req: Request, res: Response) => {
  // This can be moved to a separate referral service if needed
  // For now, keeping it simple
  controllerOperations(
    async (reqBody: any) => {
      // Simple referral code validation logic
      const { code } = reqBody;

      if (!code) {
        throw new Error('Referral code is required');
      }

      // Add your referral code validation logic here
      // For now, just return a success message
      return { message: 'Code validation logic to be implemented' };
    },
    req.body,
    res
  );
};

export const simpleRewardControllers = {
  // Core reward management
  ListRewardsHandler,
  CreateRewardHandler,
  UpdateRewardHandler,
  DeleteRewardHandler,

  // History and analytics
  GetRewardHistoryHandler,
  GetRewardStatisticsHandler,

  // Manual triggering (for testing/admin)
  TriggerInnovationIdeaRewardHandler,
  TriggerInnovationCommentRewardHandler,
  TriggerPackageReferralRewardHandler,

  // Utilities
  VerifyReferralCodeHandler,
};
