import { logger } from '../../utils/logger';
import { setupPriceModifierExpirationJob } from './priceModifierExpiration';
import { setupStaffCreditResetJob } from './staffCreditReset';
import { setupSpecialOrderAutoReceiveJob } from './specialOrderAutoReceive';
import cron from 'node-cron';

// Store all scheduled tasks so we can stop them during shutdown
const scheduledTasks: cron.ScheduledTask[] = [];

/**
 * Initialize and start all cron jobs
 */
export const setupCronJobs = (): void => {
  try {
    // Register all cron jobs here
    const priceModifierTask = setupPriceModifierExpirationJob();
    // const staffCreditResetTask = setupStaffCreditResetJob();
    const specialOrderAutoReceiveTask = setupSpecialOrderAutoReceiveJob();

    // Store the tasks for later cleanup
    if (priceModifierTask) {
      scheduledTasks.push(priceModifierTask);
    }

    // if (staffCreditResetTask) {
    //   scheduledTasks.push(staffCreditResetTask);
    // }

    if (specialOrderAutoReceiveTask) {
      scheduledTasks.push(specialOrderAutoReceiveTask);
    }

    logger.info('All cron jobs have been scheduled successfully');
  } catch (error) {
    logger.error('Error setting up cron jobs:', error);
  }
};

/**
 * Stop all running cron jobs
 * This should be called during application shutdown
 */
export const stopCronJobs = (): void => {
  try {
    // Stop all scheduled tasks
    for (const task of scheduledTasks) {
      task.stop();
    }

    logger.info(`Stopped ${scheduledTasks.length} cron jobs`);
  } catch (error) {
    logger.error('Error stopping cron jobs:', error);
  }
};
