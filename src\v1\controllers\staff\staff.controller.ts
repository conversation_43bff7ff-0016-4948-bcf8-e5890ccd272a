import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { staffService } from '../../services/staff';
import { departmentService } from '../../services/staff/department';
import { specialtyService } from '../../services/staff/specialty';
import { manualStaffCreditReset } from '../../services/staff/creditReset';

const CreateStaffHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.createStaff, req.body, res, staffId);
};

const CheckStaffHandler = (req: Request, res: Response) => {
  controllerOperations(staffService.checkStaffId, undefined, res, req.body);
};

const ForgotPasswordHandler = (req: Request, res: Response) => {
  controllerOperations(staffService.forgotPassword, undefined, res, req.body);
};

const LoginStaffHandler = (req: Request, res: Response) => {
  controllerOperations(staffService.loginStaff, undefined, res, req.body);
};

const StaffProfile = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.getStaffProfile, undefined, res, staffId);
};

const ListStaffHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.getAllStaff, req.query, res, staffId);
};

const UpdateStaffHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.updateStaff, req.body, res, staffId);
};

const UpdateStaffAdminHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.adminUpdateStaff, req.body, res, staffId);
};

const VerifyStaffCodeHandler = (req: Request, res: Response) => {
  controllerOperations(staffService.verifyStaffCode, undefined, res, req.body);
};

// const CheckStaffCodeHandler = (req: Request, res: Response) => {
//   controllerOperations(staffService.checkStaffCode, undefined, res, req.body);
// };

const VerifyStaffIdHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.verifyStaffId, req.body, res, staffId);
};

const CreateDepartmentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    departmentService.createDepartment,
    req.body,
    res,
    staffId
  );
};

const CreateUnitHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(departmentService.createUnit, req.body, res, staffId);
};

const ListDepartmentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    departmentService.getAllDepartment,
    req.query,
    res,
    staffId
  );
};

const UpdateDepartmentHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    departmentService.updateDepartment,
    req.body,
    res,
    staffId
  );
};

const ListSpecialtyHandler = (req: Request, res: Response) => {
  controllerOperations(specialtyService.getAllSpecialty, undefined, res);
};

const ListConsultantsHandler = (req: Request, res: Response) => {
  controllerOperations(staffService.getConsultants, undefined, res);
};

const CreateSpecialtyHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialtyService.createSpecialty,
    req.body,
    res,
    staffId
  );
};

const ResetStaffCreditHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(manualStaffCreditReset, undefined, res, staffId);
};

const PayCreditHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.payCredit, req.body, res, staffId);
};

const CreditStaffMealVoucherHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    staffService.ceditStaffMealVoucher,
    req.body,
    res,
    staffId
  );
};

const BlacklistStaffHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.blacklistStaff, req.body, res, staffId);
};

const UnBlacklistStaffHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(staffService.unBlacklistStaff, req.body, res, staffId);
};

export const staffControllers = {
  CreateStaffHandler,
  LoginStaffHandler,
  StaffProfile,
  ListStaffHandler,
  UpdateStaffHandler,
  VerifyStaffCodeHandler,
  VerifyStaffIdHandler,
  CreateDepartmentHandler,
  CreateUnitHandler,
  ListDepartmentHandler,
  UpdateDepartmentHandler,
  ListSpecialtyHandler,
  CreateSpecialtyHandler,
  CheckStaffHandler,
  ForgotPasswordHandler,
  ListConsultantsHandler,
  ResetStaffCreditHandler,
  UpdateStaffAdminHandler,
  PayCreditHandler,
  CreditStaffMealVoucherHandler,
  BlacklistStaffHandler,
  UnBlacklistStaffHandler
};
