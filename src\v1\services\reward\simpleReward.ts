import { db } from '../../utils/model';
import { logger } from '../../utils/logger';
import { HttpError } from '../../utils/httpError';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { RewardEventType, RewardValueType } from '@prisma/client';
import { getMainSystemAccount } from '../system';
import { formatString } from '../../utils/stringFormatter';

export const simpleRewardService = {
  createReward: async (staffId: number, reqBody: any) => {
    const {
      name,
      description,
      eventType,
      validFrom,
      validUntil,
      valueType,
      value,
      locationIds,
      maxRewardsPerUser,
      maxRewardsPerDay,
    } = reqBody;
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_CREATE);

      const formatedName = formatString.trimString(name);

      const reward = await db.$transaction(async (tx) => {
        // Check if reward with same name already exists
        const existingReward = await tx.reward.findFirst({
          where: {
            name: formatedName,
            isActive: true,
          },
        });

        if (existingReward) {
          throw new HttpError(
            'Active reward with this name already exists',
            400
          );
        }

        // Get eligible staff from specified locations
        const eligibleStaff = await tx.staff.findMany({
          where: {
            isActive: true,
            locationId: { in: locationIds.map((id: any) => Number(id)) },
          },
          select: { id: true },
        });

        // Create reward with locations and eligible staff
        return await tx.reward.create({
          data: {
            name: formatedName,
            description,
            eventType,
            valueType,
            value,
            validFrom,
            validUntil,
            maxRewardsPerUser,
            maxRewardsPerDay,
            isActive: true,
            location: {
              connect: locationIds.map((locationId: number) => ({
                id: Number(locationId),
              })),
            },
            eligibleStaff: {
              connect: eligibleStaff.map((s) => ({ id: s.id })),
            },
          },
        });
      });

      logger.info(
        `Created reward: ${reward.name} (${reward.value} ${reward.valueType} for ${reward.eventType})`
      );
      return { message: 'Reward created successfully' };
    } catch (error) {
      logger.error('Error creating reward:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to create reward', 500);
    }
  },

  listRewards: async (staffId: number, query: any = {}) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

      const rewards = await db.reward.findMany({
        where: {
          ...(query.isActive !== undefined && {
            isActive: query.isActive === 'true',
          }),
          ...(query.eventType && { eventType: query.eventType }),
          ...(query.locationId && { locationId: parseInt(query.locationId) }),
        },
        include: {
          location: {
            select: { id: true, name: true },
          },
          _count: {
            select: { staffRewards: true },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return {
        rewards,
        total: rewards.length,
      };
    } catch (error) {
      logger.error('Error listing rewards:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to list rewards', 500);
    }
  },

  updateReward: async (staffId: number, rewardId: number, data: any) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_EDIT);

      const existingReward = await db.reward.findUnique({
        where: { id: rewardId },
      });

      if (!existingReward) {
        throw new HttpError('Reward not found', 404);
      }

      // Check name uniqueness if name is being updated
      if (data.name && data.name !== existingReward.name) {
        const nameExists = await db.reward.findFirst({
          where: {
            name: data.name,
            isActive: true,
            id: { not: rewardId },
          },
        });

        if (nameExists) {
          throw new HttpError(
            'Active reward with this name already exists',
            400
          );
        }
      }

      const updatedReward = await db.reward.update({
        where: { id: rewardId },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && {
            description: data.description,
          }),
          ...(data.value && { value: data.value }),
          ...(data.maxPerUser !== undefined && {
            maxRewardsPerUser: data.maxPerUser,
          }),
          ...(data.maxPerDay !== undefined && {
            maxRewardsPerDay: data.maxPerDay,
          }),
          ...(data.isActive !== undefined && { isActive: data.isActive }),
        },
        include: {
          location: true,
        },
      });

      logger.info(`Updated reward: ${updatedReward.name}`);
      return updatedReward;
    } catch (error) {
      logger.error('Error updating reward:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to update reward', 500);
    }
  },

  deleteReward: async (staffId: number, rewardId: number) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_DELETE);

      const existingReward = await db.reward.findUnique({
        where: { id: rewardId },
      });

      if (!existingReward) {
        throw new HttpError('Reward not found', 404);
      }

      await db.reward.delete({
        where: { id: rewardId },
      });

      logger.info(`Deleted reward: ${existingReward.name}`);
      return { message: 'Reward deleted successfully' };
    } catch (error) {
      logger.error('Error deleting reward:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to delete reward', 500);
    }
  },

  triggerReward: async (
    eventType: RewardEventType,
    staffId: number,
    entityType?: string,
    entityId?: number,
    contextAmount?: number
  ) => {
    try {
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        select: { locationId: true, fullName: true },
      });

      if (!staff) {
        logger.warn(`Staff ${staffId} not found for reward trigger`);
        return [];
      }

      // Find active rewards for this event type where staff is eligible
      const rewards = await db.reward.findMany({
        where: {
          eventType,
          isActive: true,
          eligibleStaff: {
            some: {
              id: staffId,
            },
          },
        },
      });

      if (rewards.length === 0) {
        logger.debug(`No active rewards found for event type: ${eventType}`);
        return [];
      }

      const results = [];

      for (const reward of rewards) {
        try {
          // Check if user can receive this reward (respects limits)
          const canReceive = await simpleRewardService.canReceiveReward(
            staffId,
            reward.id,
            reward.maxRewardsPerUser,
            reward.maxRewardsPerDay,
            entityType,
            entityId
          );

          if (canReceive) {
            const rewardGiven = await simpleRewardService.giveReward(
              staffId,
              reward,
              entityType,
              entityId,
              contextAmount
            );
            results.push(rewardGiven);
          } else {
            logger.debug(
              `Staff ${staffId} cannot receive reward ${reward.id} due to limits`
            );
          }
        } catch (error) {
          logger.error(
            `Error processing reward ${reward.id} for staff ${staffId}:`,
            error
          );
          // Continue with other rewards even if one fails
        }
      }

      if (results.length > 0) {
        logger.info(
          `Triggered ${results.length} rewards for ${eventType} by staff ${staffId} (${staff.fullName})`
        );
      }

      return results;
    } catch (error) {
      logger.error('Error triggering rewards:', error);
      return [];
    }
  },

  canReceiveReward: async (
    staffId: number,
    rewardId: number,
    maxPerUser?: number | null,
    maxPerDay?: number | null,
    entityType?: string,
    entityId?: number
  ): Promise<boolean> => {
    try {
      // Check entity-specific reward (e.g., one reward per innovation idea)
      if (entityType && entityId) {
        const entityRewardCount = await db.rewardEventLog.count({
          where: {
            staffId,
            rewardId,
            entityType,
            entityId,
          },
        });
        if (entityRewardCount > 0) {
          return false;
        }
      }

      // Check per-user limit
      if (maxPerUser && maxPerUser > 0) {
        const userCount = await db.rewardEventLog.count({
          where: { staffId, rewardId },
        });
        if (userCount >= maxPerUser) {
          return false;
        }
      }

      // Check per-day limit (global)
      if (maxPerDay && maxPerDay > 0) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const todayCount = await db.rewardEventLog.count({
          where: {
            rewardId,
            receivedAt: {
              gte: today,
              lt: tomorrow,
            },
          },
        });
        if (todayCount >= maxPerDay) {
          return false;
        }
      }

      return true;
    } catch (error) {
      logger.error('Error checking reward eligibility:', error);
      return false;
    }
  },

  giveReward: async (
    staffId: number,
    reward: any,
    entityType?: string,
    entityId?: number,
    contextAmount?: number
  ) => {
    try {
      let transactionId: string | undefined;

      // Get staff information
      const staff = await db.staff.findUnique({
        where: { id: staffId },
        select: { locationId: true },
      });

      if (!staff) {
        throw new HttpError('Staff not found', 404);
      }

      // Calculate actual reward value
      let actualRewardValue = reward.value;
      if (reward.valueType === 'PERCENTAGE') {
        if (!contextAmount) {
          logger.warn(
            `Percentage reward ${reward.name} requires contextAmount but none provided`
          );
          return null;
        }
        actualRewardValue = (reward.value / 100) * contextAmount;
      }

      // Update staff balance/points/vouchers based on reward type
      if (reward.valueType === 'CASH' || reward.valueType === 'PERCENTAGE') {
        // Create transaction for cash rewards
        const systemAccount = await getMainSystemAccount();

        if (!systemAccount) {
          throw new HttpError('System account not found', 500);
        }

        const transaction = await db.transaction.create({
          data: {
            fromAccountId: systemAccount.id,
            toAccountId: staffId.toString(),
            amount: actualRewardValue,
            type: 'REWARD',
            status: 'SUCCESS',
            mode: 'internal',
            reference: `REWARD-${Date.now()}`,
            remarks: `Reward: ${reward.name}`,
            locationId: staff.locationId,
          },
        });

        transactionId = transaction.id;

        // Update staff wallet (assuming wallet field exists)
        await db.staff.update({
          where: { id: staffId },
          data: { wallet: { increment: actualRewardValue } },
        });
      } else if (reward.valueType === 'POINTS') {
        // Update staff points
        await db.staff.update({
          where: { id: staffId },
          data: { pointsAccrued: { increment: Math.floor(actualRewardValue) } },
        });
      } else if (reward.valueType === 'VOUCHER') {
        // Update meal voucher
        await db.staff.update({
          where: { id: staffId },
          data: { mealVoucher: { increment: actualRewardValue } },
        });
      }

      // Log the reward event
      const rewardLog = await db.rewardEventLog.create({
        data: {
          staffId,
          rewardId: reward.id,
          value: actualRewardValue,
          valueType: reward.valueType,
          currency: reward.valueType === 'CASH' ? 'NGN' : undefined,
          eventType: reward.eventType,
          eventData: {
            entityType,
            entityId,
            rewardName: reward.name,
            timestamp: new Date(),
          },
          entityType,
          entityId,
          transactionId,
        },
      });

      logger.info(
        `Rewarded staff ${staffId} with ${actualRewardValue} ${reward.valueType} for ${reward.eventType} (${reward.name})`
      );
      return rewardLog;
    } catch (error) {
      logger.error('Error giving reward:', error);
      throw error;
    }
  },

  getRewardHistory: async (staffId: number, query: any = {}) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

      const page = query.page ? parseInt(query.page) : 1;
      const limit = query.limit ? parseInt(query.limit) : 50;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (query.targetStaffId) {
        where.staffId = parseInt(query.targetStaffId);
      }

      if (query.eventType) {
        where.eventType = query.eventType;
      }

      if (query.startDate && query.endDate) {
        where.receivedAt = {
          gte: new Date(query.startDate),
          lte: new Date(query.endDate),
        };
      }

      const [logs, total] = await Promise.all([
        db.rewardEventLog.findMany({
          where,
          include: {
            staff: {
              select: {
                id: true,
                fullName: true,
                email: true,
                location: { select: { name: true } },
              },
            },
            reward: {
              select: {
                id: true,
                name: true,
                description: true,
                eventType: true,
                valueType: true,
              },
            },
          },
          orderBy: { receivedAt: 'desc' },
          take: limit,
          skip,
        }),
        db.rewardEventLog.count({ where }),
      ]);

      return {
        logs,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting reward history:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get reward history', 500);
    }
  },

  getRewardStatistics: async (staffId: number, query: any = {}) => {
    try {
      await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

      const where: any = {};

      if (query.startDate && query.endDate) {
        where.receivedAt = {
          gte: new Date(query.startDate),
          lte: new Date(query.endDate),
        };
      }

      if (query.locationId) {
        where.staff = {
          locationId: parseInt(query.locationId),
        };
      }

      const [totalRewards, totalDistributed, rewardsByType, topStaff] =
        await Promise.all([
          db.reward.count({ where: { isActive: true } }),
          db.rewardEventLog.count({ where }),
          db.rewardEventLog.groupBy({
            by: ['eventType', 'valueType'],
            where,
            _count: { id: true },
            _sum: { value: true },
          }),
          db.rewardEventLog.groupBy({
            by: ['staffId'],
            where,
            _count: { id: true },
            _sum: { value: true },
            orderBy: { _sum: { value: 'desc' } },
            take: 10,
          }),
        ]);

      // Get staff details for top staff
      const staffIds = topStaff.map((s) => s.staffId);
      const staffDetails = await db.staff.findMany({
        where: { id: { in: staffIds } },
        select: {
          id: true,
          fullName: true,
          email: true,
          location: { select: { name: true } },
        },
      });

      const topStaffWithDetails = topStaff.map((staff) => {
        const details = staffDetails.find((s) => s.id === staff.staffId);
        return {
          staffId: staff.staffId,
          fullName: details?.fullName || 'Unknown',
          email: details?.email || 'Unknown',
          location: details?.location?.name || 'Unknown',
          totalRewards: staff._count.id,
          totalValue: Number(staff._sum.value || 0),
        };
      });

      return {
        summary: {
          totalActiveRewards: totalRewards,
          totalDistributed,
          totalValue: rewardsByType.reduce(
            (sum, r) => sum + Number(r._sum.value || 0),
            0
          ),
        },
        distribution: {
          byEventType: rewardsByType.reduce((acc, curr) => {
            const existing = acc.find(
              (item) => item.eventType === curr.eventType
            );
            if (existing) {
              existing.count += curr._count.id;
              existing.value += Number(curr._sum.value || 0);
            } else {
              acc.push({
                eventType: curr.eventType,
                count: curr._count.id,
                value: Number(curr._sum.value || 0),
              });
            }
            return acc;
          }, [] as any[]),
          byValueType: rewardsByType.reduce((acc, curr) => {
            const existing = acc.find(
              (item) => item.valueType === curr.valueType
            );
            if (existing) {
              existing.count += curr._count.id;
              existing.value += Number(curr._sum.value || 0);
            } else {
              acc.push({
                valueType: curr.valueType,
                count: curr._count.id,
                value: Number(curr._sum.value || 0),
              });
            }
            return acc;
          }, [] as any[]),
          topStaff: topStaffWithDetails,
        },
      };
    } catch (error) {
      logger.error('Error getting reward statistics:', error);
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to get reward statistics', 500);
    }
  },
};
