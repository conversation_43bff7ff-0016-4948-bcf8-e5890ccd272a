import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { rewardControllers } from '../../controllers/reward/reward.controller';

export const rewardRoute = Router();

// Core Reward Management Routes (Simplified)
rewardRoute.get('/list', secure, rewardControllers.ListRewardHandler);
rewardRoute.post('/create', secure, rewardControllers.CreateRewardHandler);
rewardRoute.patch('/update', secure, rewardControllers.UpdateRewardHandler);
rewardRoute.delete('/:rewardId', secure, rewardControllers.DeleteRewardHandler);

// Referral Code Validation
rewardRoute.post(
  '/referral/validate',
  rewardControllers.VerifyReferralCodeHandler
);

// Manual Reward Triggering (for testing/admin)
rewardRoute.post(
  '/trigger/innovation-idea',
  secure,
  rewardControllers.TriggerInnovationIdeaRewardHandler
);
rewardRoute.post(
  '/trigger/innovation-comment',
  secure,
  rewardControllers.TriggerInnovationCommentRewardHandler
);
rewardRoute.post(
  '/trigger/package-referral',
  secure,
  rewardControllers.TriggerPackageReferralRewardHandler
);

// Statistics and History Routes
rewardRoute.get(
  '/statistics',
  secure,
  rewardControllers.GetRewardStatisticsHandler
);
rewardRoute.get('/history', secure, rewardControllers.GetRewardHistoryHandler);

// Simple reward system - no complex analytics or automatic calculations needed
