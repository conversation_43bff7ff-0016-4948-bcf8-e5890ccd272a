import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { suggestionControllers } from '../../controllers/suggestion/suggestion.controller';

export const suggestionRoute = Router();

// Create a new suggestion
suggestionRoute.post(
  '/create',
  secure,
  suggestionControllers.CreateSuggestionHandler
);

// Get all suggestions with pagination and filtering
// GET /suggestions/list?page=1&limit=10&search=keyword&isRead=false&anonymous=true&myIdeas=true&startDate=2024-01-01&endDate=2024-12-31&sortBy=createdAt&sortOrder=desc
suggestionRoute.get(
  '/list',
  secure,
  suggestionControllers.ListSuggestionsHandler
);

// Get a single suggestion by ID
suggestionRoute.get(
  '/:suggestionId',
  secure,
  suggestionControllers.GetSuggestionHandler
);

// Update a suggestion (mark as read, add comment)
suggestionRoute.patch(
  '/update',
  secure,
  suggestionControllers.UpdateSuggestionHandler
);

// Delete a suggestion
suggestionRoute.delete(
  '/:suggestionId',
  secure,
  suggestionControllers.DeleteSuggestionHandler
);
