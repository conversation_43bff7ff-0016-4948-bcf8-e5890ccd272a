import { Request, Response } from 'express';
import { controllerOperations } from '../handlers/handleController';
import { rewardService } from '../../services/reward/reward';
import { simpleTriggers } from '../../services/reward/simpleTriggers';

const ListRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.getAllRewards, req.query, res, staffId);
};

const UpdateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { rewardId, ...updateData } = req.body;
  controllerOperations(
    rewardService.updateReward,
    updateData,
    res,
    staffId,
    rewardId
  );
};

const CreateRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.createReward, req.body, res, staffId);
};

const VerifyReferralCodeHandler = (req: Request, res: Response) => {
  controllerOperations(
    rewardService.verifyReferralCode,
    undefined,
    res,
    req.body
  );
};

const DeleteRewardHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { rewardId } = req.params;
  controllerOperations(
    rewardService.deleteReward,
    undefined,
    res,
    staffId,
    parseInt(rewardId)
  );
};

// Manual Reward Triggering (for testing/admin purposes)
const TriggerInnovationIdeaRewardHandler = (req: Request, res: Response) => {
  const { staffId, ideaId } = req.body;
  controllerOperations(
    simpleTriggers.onInnovationIdeaPosted,
    undefined,
    res,
    staffId,
    ideaId
  );
};

const TriggerInnovationCommentRewardHandler = (req: Request, res: Response) => {
  const { staffId, commentId, ideaId } = req.body;
  controllerOperations(
    simpleTriggers.onInnovationComment,
    undefined,
    res,
    staffId,
    commentId,
    ideaId
  );
};

const TriggerPackageReferralRewardHandler = (req: Request, res: Response) => {
  const { referrerStaffId, bookingId, referralCodeId } = req.body;
  controllerOperations(
    simpleTriggers.onPackageReferral,
    undefined,
    res,
    referrerStaffId,
    bookingId,
    referralCodeId
  );
};

// Statistics and History Handlers
const GetRewardStatisticsHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    rewardService.getRewardStatistics,
    req.query,
    res,
    staffId
  );
};

const GetRewardHistoryHandler = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(rewardService.getRewardHistory, req.query, res, staffId);
};

// Simple handlers only - no complex analytics or automatic calculations needed

export const rewardControllers = {
  // Core reward management (simple and clean)
  ListRewardHandler,
  CreateRewardHandler,
  UpdateRewardHandler,
  DeleteRewardHandler,
  VerifyReferralCodeHandler,

  // Manual reward triggering (for testing/admin)
  TriggerInnovationIdeaRewardHandler,
  TriggerInnovationCommentRewardHandler,
  TriggerPackageReferralRewardHandler,

  // Statistics and history
  GetRewardStatisticsHandler,
  GetRewardHistoryHandler,
};
